"""
ETL Consolidado - Cliente Salesforce Marketing Cloud
Cliente otimizado para integração com SFMC com batch processing e monitoramento assíncrono.
"""

import time
import json
import logging
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional, Union
import requests
from requests.adapters import HTTPAdapter
from urllib3.util.retry import Retry
import pandas as pd

from salesforce_integration.config import SALESFORCE_CONFIG, DATA_EXTENSIONS, RETRY_CONFIGS, OPERATION_MODE
from salesforce_integration.utils import retry_decorator, ProgressTracker, mask_sensitive_data

# =============================================================================
# CONFIGURAÇÃO DE LOGGING
# =============================================================================

logger = logging.getLogger(__name__)

# =============================================================================
# FUNÇÕES UTILITÁRIAS
# =============================================================================

def convert_dataframe_to_records(data: Union[pd.DataFrame, List[Dict[str, Any]]]) -> List[Dict[str, Any]]:
    """
    Converte DataFrame para lista de dicionários, ou retorna a lista se já for uma.
    Trata valores float inválidos (inf, -inf, NaN) e normaliza dados para Salesforce.

    Args:
        data: DataFrame do pandas ou lista de dicionários

    Returns:
        Lista de dicionários com valores JSON-compatíveis e normalizados
    """
    if isinstance(data, pd.DataFrame):
        # Cria uma cópia para não modificar o original
        df_clean = data.copy()

        # Substitui valores float inválidos por None
        # inf, -inf, NaN não são JSON-compatíveis
        df_clean = df_clean.replace([float('inf'), float('-inf')], None)
        df_clean = df_clean.where(pd.notnull(df_clean), None)

        # Converte para lista de dicionários
        records = df_clean.to_dict('records')

        # Aplica limpeza de valores float inválidos
        records = clean_float_values_in_records(records)

        # Aplica normalização para Salesforce
        return normalize_salesforce_data(records)
    elif isinstance(data, list):
        # Se já é uma lista, aplica limpeza e normalização
        records = clean_float_values_in_records(data)
        return normalize_salesforce_data(records)
    else:
        raise ValueError(f"Tipo de dados não suportado: {type(data)}")

def clean_float_values_in_records(records: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
    """
    Limpa valores float inválidos em uma lista de dicionários.

    Args:
        records: Lista de dicionários

    Returns:
        Lista de dicionários com valores float limpos
    """
    import math
    import numpy as np

    cleaned_records = []
    for record in records:
        cleaned_record = {}
        for key, value in record.items():
            # Verifica diferentes tipos de valores problemáticos
            if value is None:
                cleaned_record[key] = None
            elif isinstance(value, float):
                if math.isnan(value) or math.isinf(value):
                    cleaned_record[key] = None
                else:
                    cleaned_record[key] = value
            elif isinstance(value, np.floating):
                # Valores numpy float
                if np.isnan(value) or np.isinf(value):
                    cleaned_record[key] = None
                else:
                    cleaned_record[key] = float(value)
            elif pd.isna(value):
                # Valores pandas NA/NaT
                cleaned_record[key] = None
            else:
                cleaned_record[key] = value
        cleaned_records.append(cleaned_record)

    return cleaned_records

def normalize_field_names(field_name: str) -> str:
    """
    Normaliza nomes de campos para o formato esperado pelo Salesforce.

    Args:
        field_name: Nome do campo original

    Returns:
        Nome do campo normalizado
    """
    # Mapeamento de campos conhecidos
    field_mapping = {
        'CNPJCPF': 'cnpjcpf',
        'PrimeiroNome': 'primeironome',
        'NomeCompleto': 'nomecompleto',
        'Cidade': 'cidade',
        'Estado': 'estado',
        'Celular': 'celular',
        'Genero': 'genero',
        'PontoVenda': 'pontovenda',
        'Locale': 'locale',
        'Origem': 'origem',
        'Campaign': 'campaign',
        'Source': 'source',
        'Medium': 'medium',
        'Term': 'term',
        'Content': 'content',
        'Renda': 'renda',
        'dt_nascimento': 'dt_nascimento',
        'dt_cadastro': 'dt_cadastro',
        'id_documento': 'id_documento',
        'end_point': 'end_point',
        'email': 'email'
    }

    # Se há mapeamento específico, usa ele
    if field_name in field_mapping:
        return field_mapping[field_name]

    # Senão, converte para minúsculo
    return field_name.lower()

def normalize_salesforce_data(records: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
    """
    Normaliza dados para compatibilidade com Salesforce Marketing Cloud.

    Args:
        records: Lista de dicionários com dados brutos

    Returns:
        Lista de dicionários normalizados
    """
    import math

    normalized_records = []

    for record in records:
        normalized_record = {}

        for key, value in record.items():
            # Normaliza nome do campo
            normalized_key = normalize_field_names(key)

            # Remove espaços em branco de strings
            if isinstance(value, str):
                value = value.strip()
                # Converte strings vazias para None
                if value == '':
                    value = None

            # Converte números grandes para string se necessário
            elif isinstance(value, (int, float)) and not isinstance(value, bool):
                # Se é um número muito grande (como ID), converte para string
                if isinstance(value, int) and value > 999999999:  # Números com mais de 9 dígitos
                    value = str(value)
                # Se é float, mantém como número mas limita casas decimais
                elif isinstance(value, float) and not (math.isnan(value) or math.isinf(value)):
                    value = round(value, 2)

            # Remove campos com nomes problemáticos (começam com números, etc.)
            if normalized_key and isinstance(normalized_key, str) and normalized_key[0].isalpha():
                normalized_record[normalized_key] = value

        normalized_records.append(normalized_record)

    return normalized_records

def is_data_empty(data: Union[pd.DataFrame, List[Dict[str, Any]], None]) -> bool:
    """
    Verifica se os dados estão vazios - compatível com DataFrame e List.

    Args:
        data: DataFrame, lista de dicionários ou None

    Returns:
        True se os dados estão vazios, False caso contrário
    """
    if data is None:
        return True
    elif isinstance(data, pd.DataFrame):
        return data.empty
    elif isinstance(data, list):
        return len(data) == 0
    else:
        return False



# =============================================================================
# CLASSE PRINCIPAL SALESFORCE CLIENT
# =============================================================================

class SalesforceClient:
    """Cliente otimizado para Salesforce Marketing Cloud"""
    
    def __init__(self):
        self.client_id = SALESFORCE_CONFIG['client_id']
        self.client_secret = SALESFORCE_CONFIG['client_secret']
        self.auth_uri = SALESFORCE_CONFIG['auth_uri']
        self.rest_uri = SALESFORCE_CONFIG['rest_uri']
        self.timeout = SALESFORCE_CONFIG['timeout']
        self.batch_size = SALESFORCE_CONFIG['batch_size']
        
        # Token de autenticação
        self.access_token = None
        self.token_expires_at = None
        
        # Configuração de sessão HTTP
        self.session = requests.Session()
        self._setup_session()
        
        # Métricas
        self.metrics = {
            'requests_made': 0,
            'successful_requests': 0,
            'failed_requests': 0,
            'records_processed': 0,
            'batches_processed': 0,
            'total_time': 0
        }
        
        logger.info("SalesforceClient inicializado")
    
    def _setup_session(self):
        """Configura sessão HTTP com retry automático"""
        retry_strategy = Retry(
            total=RETRY_CONFIGS['max_attempts'],
            backoff_factor=RETRY_CONFIGS['base_delay'],
            status_forcelist=[429, 500, 502, 503, 504],
            allowed_methods=["HEAD", "GET", "POST", "PUT", "DELETE", "OPTIONS", "TRACE"]
        )
        
        adapter = HTTPAdapter(max_retries=retry_strategy)
        self.session.mount("http://", adapter)
        self.session.mount("https://", adapter)
    
    @retry_decorator(max_attempts=3, delay=2, exponential_backoff=True)
    def authenticate(self) -> bool:
        """Autentica com Salesforce e obtém token de acesso"""
        # Verifica se token ainda é válido
        if self.access_token and self.token_expires_at:
            if datetime.now() < self.token_expires_at - timedelta(minutes=5):
                return True
        
        logger.info("Autenticando com Salesforce Marketing Cloud...")
        
        auth_payload = {
            'grant_type': 'client_credentials',
            'client_id': self.client_id,
            'client_secret': self.client_secret
        }
        
        try:
            response = self.session.post(
                f"{self.auth_uri}v2/token",
                json=auth_payload,
                timeout=30
            )
            
            self.metrics['requests_made'] += 1
            
            if response.status_code == 200:
                auth_data = response.json()
                self.access_token = auth_data['access_token']
                expires_in = auth_data.get('expires_in', 3600)
                self.token_expires_at = datetime.now() + timedelta(seconds=expires_in)
                
                # Atualiza headers da sessão
                self.session.headers.update({
                    'Authorization': f'Bearer {self.access_token}',
                    'Content-Type': 'application/json'
                })
                
                self.metrics['successful_requests'] += 1
                logger.info("✅ Autenticação realizada com sucesso")
                return True
            else:
                self.metrics['failed_requests'] += 1
                logger.error(f"❌ Erro na autenticação: {response.status_code} - {response.text}")
                return False
                
        except Exception as e:
            self.metrics['failed_requests'] += 1
            logger.error(f"❌ Erro na autenticação: {e}")
            return False
    


    def validate_data_extension(self, table_name: str) -> bool:
        """Valida se Data Extension existe e está acessível"""
        if not self.authenticate():
            return False

        config = DATA_EXTENSIONS.get(table_name)
        if not config:
            logger.error(f"❌ Configuração para tabela '{table_name}' não encontrada")
            return False

        external_key = config['external_key']

        try:
            url = f"{self.rest_uri}data/v1/customobjectdata/key/{external_key}/rowset"
            response = self.session.get(url, timeout=30)

            self.metrics['requests_made'] += 1

            if response.status_code in [200, 404]:  # 404 é OK se não houver dados
                self.metrics['successful_requests'] += 1
                logger.info(f"✅ Data Extension '{table_name}' validada com sucesso")
                return True
            else:
                self.metrics['failed_requests'] += 1
                logger.error(f"❌ Erro na validação de '{table_name}': {response.status_code}")
                return False

        except Exception as e:
            self.metrics['failed_requests'] += 1
            logger.error(f"❌ Erro na validação de '{table_name}': {e}")
            return False
    
    def send_data_batch(self, table_name: str, data_batch: List[Dict[str, Any]]) -> Dict[str, Any]:
        """Envia lote de dados para Salesforce de forma assíncrona"""
        if not self.authenticate():
            return {'success': False, 'error': 'Falha na autenticação'}
        
        config = DATA_EXTENSIONS.get(table_name)
        if not config:
            return {'success': False, 'error': f'Configuração para {table_name} não encontrada'}
        
        external_key = config['external_key']
        
        # Prepara payload
        payload = {
            'items': data_batch
        }

        # Verifica se o payload é JSON-serializável antes do envio
        try:
            import json
            json.dumps(payload)
        except (TypeError, ValueError) as json_error:
            logger.error(f"❌ Payload não é JSON-serializável: {json_error}")
            return {
                'success': False,
                'error': f'Dados não são JSON-compatíveis: {json_error}'
            }

        start_time = time.time()

        try:
            # Envia dados de forma assíncrona usando o endpoint correto
            url = f"{self.rest_uri}data/v1/async/dataextensions/key:{external_key}/rows"
            response = self.session.put(  # PUT para upsert
                url,
                json=payload,
                timeout=self.timeout
            )
            
            self.metrics['requests_made'] += 1
            processing_time = time.time() - start_time
            
            if response.status_code == 202:  # Accepted - processamento assíncrono
                result_data = response.json()
                request_id = result_data.get('requestId')
                
                self.metrics['successful_requests'] += 1
                self.metrics['records_processed'] += len(data_batch)
                self.metrics['batches_processed'] += 1
                self.metrics['total_time'] += processing_time
                
                logger.info(
                    f"✅ Lote enviado para '{table_name}': "
                    f"{len(data_batch)} registros, "
                    f"Request ID: {request_id}, "
                    f"Tempo: {processing_time:.2f}s"
                )
                
                return {
                    'success': True,
                    'request_id': request_id,
                    'records_count': len(data_batch),
                    'processing_time': processing_time
                }
            else:
                self.metrics['failed_requests'] += 1
                error_msg = f"Status: {response.status_code}, Response: {response.text}"
                logger.error(f"❌ Erro no envio para '{table_name}': {error_msg}")
                
                return {
                    'success': False,
                    'error': error_msg,
                    'status_code': response.status_code
                }
                
        except Exception as e:
            self.metrics['failed_requests'] += 1
            logger.error(f"❌ Erro no envio para '{table_name}': {e}")
            
            return {
                'success': False,
                'error': str(e)
            }
    
    def wait_for_completion(self, request_id: str, max_wait_time: int = None) -> Dict[str, Any]:
        """Aguarda conclusão do processamento assíncrono com polling inteligente"""
        max_wait_time = max_wait_time or SALESFORCE_CONFIG['max_polling_time']

        # Polling inteligente: intervalo adaptativo
        polling_intervals = [2, 5, 10, 15, 30]  # Intervalos progressivos
        current_interval_idx = 0

        start_time = time.time()

        logger.info(f"Aguardando conclusão do Request ID: {request_id}")

        while time.time() - start_time < max_wait_time:
            try:
                url = f"{self.rest_uri}data/v1/async/{request_id}/status"
                response = self.session.get(url, timeout=30)
                
                self.metrics['requests_made'] += 1
                
                if response.status_code == 200:
                    result_data = response.json()
                    status = result_data.get('status', 'Unknown')
                    
                    self.metrics['successful_requests'] += 1
                    
                    if status == 'Complete':
                        processing_time = time.time() - start_time
                        logger.info(f"✅ Processamento concluído: {request_id} ({processing_time:.2f}s)")
                        return {
                            'success': True,
                            'status': status,
                            'result': result_data,
                            'processing_time': processing_time
                        }
                    elif status == 'Error':
                        logger.error(f"❌ Erro no processamento: {request_id}")
                        return {
                            'success': False,
                            'status': status,
                            'error': result_data.get('message', 'Erro desconhecido')
                        }
                    else:
                        # Ainda processando - usa polling inteligente
                        elapsed_time = time.time() - start_time
                        current_interval = polling_intervals[min(current_interval_idx, len(polling_intervals) - 1)]
                        
                        logger.debug(f"Status: {status}, aguardando... ({elapsed_time:.1f}s)")
                        time.sleep(current_interval)
                        
                        # Aumenta intervalo gradualmente
                        if current_interval_idx < len(polling_intervals) - 1:
                            current_interval_idx += 1
                        continue
                else:
                    self.metrics['failed_requests'] += 1
                    logger.warning(f"⚠️ Erro ao consultar status: {response.status_code}")
                    time.sleep(polling_intervals[0])
                    continue
                    
            except Exception as e:
                self.metrics['failed_requests'] += 1
                logger.warning(f"⚠️ Erro ao consultar status: {e}")
                time.sleep(polling_intervals[0])
                continue
        
        # Timeout
        logger.warning(f"⏱️ Timeout aguardando conclusão: {request_id}")
        return {
            'success': False,
            'error': 'Timeout aguardando conclusão',
            'status': 'Timeout'
        }
    
    def wait_for_all_batches(self, request_ids: List[str], max_wait_time: int = None) -> Dict[str, Any]:
        """Aguarda conclusão de múltiplos lotes em paralelo com timeout inteligente"""
        if not request_ids:
            return {'all_completed': True, 'results': []}

        # Timeout inteligente: 45 segundos por lote + 10 minutos base
        estimated_time = len(request_ids) * 45 + 600
        max_wait_time = max_wait_time or min(estimated_time, 2700)  # Máximo 45 minutos

        start_time = time.time()

        logger.info(f"Aguardando conclusão de {len(request_ids)} lotes (timeout: {max_wait_time}s)...")

        # Rastreia status de cada lote
        batch_status = {req_id: {'status': 'pending', 'result': None} for req_id in request_ids}
        completed_count = 0
        consecutive_no_progress = 0

        progress_tracker = ProgressTracker(
            total=len(request_ids),
            description="Aguardando conclusão dos lotes"
        )

        while completed_count < len(request_ids) and time.time() - start_time < max_wait_time:
            previous_completed = completed_count

            # Verifica status de cada lote pendente
            for request_id in request_ids:
                if batch_status[request_id]['status'] == 'pending':
                    result = self.get_batch_status(request_id)

                    # Status aceitos como conclusão (incluindo variações)
                    completed_statuses = ['Complete', 'Completed', 'Success', 'OK', 'Error', 'Failed', 'Timeout']

                    if result['status'] in completed_statuses or not result['success']:
                        batch_status[request_id]['status'] = result['status']
                        batch_status[request_id]['result'] = result
                        completed_count += 1
                        progress_tracker.update()

                        if result['status'] in ['Complete', 'Completed', 'Success', 'OK']:
                            logger.debug(f"✅ Lote concluído: {request_id}")
                        else:
                            logger.warning(f"❌ Lote com erro: {request_id} - Status: {result['status']}")

            # Detecta se houve progresso
            if completed_count == previous_completed:
                consecutive_no_progress += 1
            else:
                consecutive_no_progress = 0

            # Se não há progresso por muito tempo, força conclusão
            if consecutive_no_progress >= 24:  # 2 minutos sem progresso
                logger.warning(f"⚠️ Sem progresso por 2 minutos. Forçando conclusão...")
                break

            # Aguarda antes da próxima verificação
            if completed_count < len(request_ids):
                time.sleep(5)  # Intervalo fixo para verificação múltipla
        
        progress_tracker.finish()
        
        # Calcula estatísticas finais - verifica se há erros dentro dos lotes
        successful_batches = 0
        failed_batches = 0
        pending_batches = 0
        
        for status_info in batch_status.values():
            if status_info['status'] == 'Complete':
                # Verifica se há erros dentro do resultado
                result = status_info.get('result', {})
                has_errors = False
                result_status = ''
                
                # Estrutura: result -> result -> status -> {hasErrors, resultStatus}
                if isinstance(result, dict):
                    inner_result = result.get('result', {})
                    if isinstance(inner_result, dict) and 'status' in inner_result:
                        nested_status = inner_result['status']
                        if isinstance(nested_status, dict):
                            has_errors = nested_status.get('hasErrors', False)
                            result_status = nested_status.get('resultStatus', '')
                    # Também verifica estrutura direta: result -> status
                    elif 'status' in result:
                        nested_status = result['status']
                        if isinstance(nested_status, dict):
                            has_errors = nested_status.get('hasErrors', False)
                            result_status = nested_status.get('resultStatus', '')
                        else:
                            result_status = str(nested_status)
                    
                    if has_errors or result_status == 'Error':
                        failed_batches += 1
                        logger.error(f"❌ Lote com erros: hasErrors={has_errors}, resultStatus={result_status}")
                        
                        # Extrai detalhes dos erros se disponíveis
                        result_messages = inner_result.get('resultMessages', []) if inner_result else result.get('resultMessages', [])
                        if result_messages:
                            logger.error(f"Mensagens de erro: {result_messages}")
                        
                        # Log da estrutura completa para debug
                        logger.error(f"Estrutura completa do resultado com erro: {result}")
                    else:
                        successful_batches += 1
                else:
                    successful_batches += 1
            elif status_info['status'] == 'Error':
                failed_batches += 1
            else:
                pending_batches += 1
        
        all_completed = completed_count == len(request_ids)
        
        result = {
            'all_completed': all_completed,
            'successful_batches': successful_batches,
            'failed_batches': failed_batches,
            'pending_batches': pending_batches,
            'total_batches': len(request_ids),
            'processing_time': time.time() - start_time,
            'results': batch_status
        }
        
        if all_completed:
            logger.info(f"✅ Todos os lotes concluídos: {successful_batches} sucessos, {failed_batches} falhas")
        else:
            logger.warning(f"⚠️ Timeout: {successful_batches} sucessos, {failed_batches} falhas, {pending_batches} pendentes")
        
        return result
    
    def get_batch_status(self, request_id: str) -> Dict[str, Any]:
        """Obtém status de um lote específico sem polling"""
        try:
            url = f"{self.rest_uri}data/v1/async/{request_id}/status"
            response = self.session.get(url, timeout=30)

            self.metrics['requests_made'] += 1

            if response.status_code == 200:
                result_data = response.json()

                # Extrai status corretamente da estrutura do Salesforce
                status = 'Unknown'

                # Verifica se há status aninhado (estrutura normal do Salesforce)
                if 'status' in result_data and isinstance(result_data['status'], dict):
                    nested_status = result_data['status']
                    if 'requestStatus' in nested_status:
                        status = nested_status['requestStatus']
                    elif 'status' in nested_status:
                        status = nested_status['status']
                # Verifica se há status direto
                elif 'requestStatus' in result_data:
                    status = result_data['requestStatus']
                elif 'status' in result_data and isinstance(result_data['status'], str):
                    status = result_data['status']

                # Log detalhado para debug
                logger.debug(f"Status do lote {request_id}: {status} - Dados: {result_data}")

                self.metrics['successful_requests'] += 1

                return {
                    'success': True,
                    'status': status,
                    'result': result_data
                }
            elif response.status_code == 404:
                # 404 pode indicar que o lote foi processado e removido
                logger.debug(f"Lote {request_id} não encontrado (404) - assumindo como concluído")
                self.metrics['successful_requests'] += 1
                return {
                    'success': True,
                    'status': 'Complete',
                    'result': {'status': 'Complete', 'message': 'Batch processed and removed'}
                }
            else:
                self.metrics['failed_requests'] += 1
                logger.warning(f"Erro HTTP {response.status_code} para lote {request_id}: {response.text}")
                return {
                    'success': False,
                    'status': 'Error',
                    'error': f'HTTP {response.status_code}: {response.text}'
                }

        except Exception as e:
            self.metrics['failed_requests'] += 1
            logger.warning(f"Exceção ao verificar status do lote {request_id}: {e}")
            return {
                'success': False,
                'status': 'Error',
                'error': str(e)
            }
    
    def get_async_results(self, request_id: str) -> Dict[str, Any]:
        """Obtém resultados detalhados do processamento assíncrono"""
        try:
            url = f"{self.rest_uri}data/v1/async/{request_id}/status"
            response = self.session.get(url, timeout=30)
            
            self.metrics['requests_made'] += 1
            
            if response.status_code == 200:
                self.metrics['successful_requests'] += 1
                return response.json()
            else:
                self.metrics['failed_requests'] += 1
                return {'error': f'Status: {response.status_code}'}
                
        except Exception as e:
            self.metrics['failed_requests'] += 1
            return {'error': str(e)}
    
    def process_table_data(self, table_name: str, data: Union[pd.DataFrame, List[Dict[str, Any]]],
                          dry_run: bool = False, wait_for_completion: bool = True,
                          skip_status_check: bool = False) -> Dict[str, Any]:
        """Processa dados completos de uma tabela em lotes com monitoramento otimizado"""
        # Verifica se data está vazio - compatível com DataFrame e List
        if is_data_empty(data):
            return {'success': True, 'message': 'Nenhum dado para processar'}

        # Converte DataFrame para lista de dicionários se necessário
        data_records = convert_dataframe_to_records(data)

        # Valida Data Extension
        if not dry_run and not self.validate_data_extension(table_name):
            return {'success': False, 'error': 'Validação de Data Extension falhou'}

        # Calcula lotes
        batch_size = min(self.batch_size, len(data_records))
        total_batches = (len(data_records) + batch_size - 1) // batch_size

        logger.info(
            f"Processando '{table_name}': "
            f"{len(data_records)} registros em {total_batches} lotes"
        )
        
        # Inicializa tracker de progresso
        progress_tracker = ProgressTracker(
            total=total_batches,
            description=f"Processando {table_name}"
        )
        
        results = {
            'success': True,
            'table_name': table_name,
            'total_records': len(data_records),
            'total_batches': total_batches,
            'processed_batches': 0,
            'failed_batches': 0,
            'request_ids': [],
            'errors': [],
            'batch_details': []
        }

        # Processa cada lote
        for i in range(0, len(data_records), batch_size):
            batch_data = data_records[i:i + batch_size]
            batch_number = (i // batch_size) + 1
            
            if dry_run:
                logger.info(f"DRY RUN - Lote {batch_number}/{total_batches}: {len(batch_data)} registros")
                time.sleep(0.1)  # Simula processamento
                results['processed_batches'] += 1
                results['batch_details'].append({
                    'batch_number': batch_number,
                    'records_count': len(batch_data),
                    'status': 'success_dry_run'
                })
            else:
                # Envia lote real
                batch_result = self.send_data_batch(table_name, batch_data)
                
                if batch_result['success']:
                    results['processed_batches'] += 1
                    results['request_ids'].append(batch_result['request_id'])
                    results['batch_details'].append({
                        'batch_number': batch_number,
                        'records_count': len(batch_data),
                        'request_id': batch_result['request_id'],
                        'status': 'sent',
                        'processing_time': batch_result.get('processing_time', 0)
                    })
                else:
                    results['failed_batches'] += 1
                    results['errors'].append(f"Lote {batch_number}: {batch_result['error']}")
                    results['batch_details'].append({
                        'batch_number': batch_number,
                        'records_count': len(batch_data),
                        'status': 'failed',
                        'error': batch_result['error']
                    })
                    logger.error(f"❌ Falha no lote {batch_number}: {batch_result['error']}")
            
            progress_tracker.update()
        
        progress_tracker.finish()
        
        # Monitora conclusão dos lotes se solicitado
        if wait_for_completion and not dry_run and not skip_status_check and results['request_ids']:
            logger.info(f"Aguardando conclusão de {len(results['request_ids'])} lotes...")
            completion_results = self.wait_for_all_batches(results['request_ids'])
            results['completion_results'] = completion_results

            # Atualiza status final baseado na conclusão E nos erros
            if completion_results['all_completed'] and completion_results['failed_batches'] == 0:
                results['success'] = True
                logger.info(f"✅ Todos os lotes de '{table_name}' concluídos com sucesso")
            else:
                results['success'] = False
                if completion_results['failed_batches'] > 0:
                    logger.error(f"❌ {completion_results['failed_batches']} lotes de '{table_name}' falharam com erros")
                else:
                    logger.warning(f"⚠️ Alguns lotes de '{table_name}' não concluíram")
        elif skip_status_check and results['request_ids']:
            logger.info(f"⚠️ Verificação de status pulada para {len(results['request_ids'])} lotes")
            results['success'] = True
            logger.info(f"✅ Processamento de '{table_name}' concluído (status não verificado)")
        
        # Resultado final
        if results['failed_batches'] == 0:
            logger.info(f"✅ Processamento de '{table_name}' concluído com sucesso")
            results['success'] = True
        else:
            logger.warning(f"⚠️ Processamento de '{table_name}' com falhas")
            results['success'] = False
        
        return results
    
    def get_metrics(self) -> Dict[str, Any]:
        """Retorna métricas do cliente"""
        return {
            **self.metrics,
            'success_rate': (
                self.metrics['successful_requests'] / self.metrics['requests_made'] * 100
                if self.metrics['requests_made'] > 0 else 0
            ),
            'average_processing_time': (
                self.metrics['total_time'] / self.metrics['batches_processed']
                if self.metrics['batches_processed'] > 0 else 0
            )
        }
    
    def reset_metrics(self):
        """Reseta métricas"""
        self.metrics = {
            'requests_made': 0,
            'successful_requests': 0,
            'failed_requests': 0,
            'records_processed': 0,
            'batches_processed': 0,
            'total_time': 0
        }
        logger.info("Métricas resetadas")
    
    def process_multiple_tables(self, tables_data: Dict[str, Union[pd.DataFrame, List[Dict[str, Any]]]],
                               dry_run: bool = False,
                               table_sequence: List[str] = None,
                               skip_status_check: bool = True) -> Dict[str, Any]:
        """
        Processa múltiplas tabelas na sequência correta:
        Produtos → Clientes → Leads → Propostas
        """
        # Sequência padrão baseada no planning.md
        default_sequence = ['tb_produtos', 'tb_clientes', 'tb_leads', 'tb_propostas']
        sequence = table_sequence or default_sequence
        
        # Filtra apenas tabelas que têm dados - compatível com DataFrame e List
        available_tables = [
            table for table in sequence
            if table in tables_data and not is_data_empty(tables_data[table])
        ]
        
        if not available_tables:
            return {
                'success': True,
                'message': 'Nenhuma tabela com dados para processar',
                'tables_processed': []
            }
        
        logger.info(f"Processando {len(available_tables)} tabelas na sequência: {' → '.join(available_tables)}")
        
        pipeline_results = {
            'success': True,
            'pipeline_start_time': time.time(),
            'tables_processed': [],
            'tables_failed': [],
            'total_records': 0,
            'processing_summary': {}
        }
        
        # Processa cada tabela na sequência
        for table_name in available_tables:
            table_data = tables_data[table_name]

            # Calcula número de registros para log (compatível com DataFrame e List)
            record_count = len(table_data) if hasattr(table_data, '__len__') else 0
            logger.info(f"Iniciando processamento de '{table_name}' - {record_count} registros")
            
            # Processa tabela individual
            table_result = self.process_table_data(
                table_name=table_name,
                data=table_data,
                dry_run=dry_run,
                wait_for_completion=not skip_status_check,
                skip_status_check=skip_status_check
            )
            
            # Registra resultado
            pipeline_results['processing_summary'][table_name] = table_result
            pipeline_results['total_records'] += table_result.get('total_records', 0)
            
            if table_result['success']:
                pipeline_results['tables_processed'].append(table_name)
                logger.info(f"✅ '{table_name}' processado com sucesso")
            else:
                pipeline_results['tables_failed'].append(table_name)
                logger.error(f"❌ Falha no processamento de '{table_name}'")
                
                # Opcional: parar pipeline em caso de falha
                # pipeline_results['success'] = False
                # break
        
        # Calcula estatísticas finais
        pipeline_results['pipeline_end_time'] = time.time()
        pipeline_results['total_processing_time'] = (
            pipeline_results['pipeline_end_time'] - pipeline_results['pipeline_start_time']
        )
        
        pipeline_results['success'] = len(pipeline_results['tables_failed']) == 0
        
        # Log final
        if pipeline_results['success']:
            logger.info(
                f"✅ Pipeline concluído com sucesso: "
                f"{len(pipeline_results['tables_processed'])} tabelas, "
                f"{pipeline_results['total_records']} registros, "
                f"{pipeline_results['total_processing_time']:.2f}s"
            )
        else:
            logger.warning(
                f"⚠️ Pipeline com falhas: "
                f"{len(pipeline_results['tables_processed'])} sucessos, "
                f"{len(pipeline_results['tables_failed'])} falhas"
            )
        
        return pipeline_results
    
    def close(self):
        """Fecha cliente e limpa recursos"""
        if self.session:
            self.session.close()
        logger.info("SalesforceClient finalizado")

# =============================================================================
# FUNÇÕES DE CONVENIÊNCIA
# =============================================================================

def create_salesforce_client() -> SalesforceClient:
    """Cria instância do cliente Salesforce"""
    return SalesforceClient()

def send_data_to_salesforce(table_name: str, data: Union[pd.DataFrame, List[Dict[str, Any]]],
                           dry_run: bool = False, skip_status_check: bool = False) -> Dict[str, Any]:
    """Função de conveniência para envio de dados"""
    client = create_salesforce_client()

    try:
        result = client.process_table_data(table_name, data, dry_run, skip_status_check=skip_status_check)
        return result
    finally:
        client.close()

def send_data_to_salesforce_fast(table_name: str, data: Union[pd.DataFrame, List[Dict[str, Any]]],
                                 dry_run: bool = False) -> Dict[str, Any]:
    """Função de conveniência para envio rápido (sem verificação de status)"""
    return send_data_to_salesforce(table_name, data, dry_run, skip_status_check=True)

def process_etl_pipeline_with_verification(tables_data: Dict[str, Union[pd.DataFrame, List[Dict[str, Any]]]],
                                          dry_run: bool = False,
                                          table_sequence: List[str] = None) -> Dict[str, Any]:
    """Pipeline ETL com verificação de status (mais lento, mas mais seguro)"""
    return process_etl_pipeline(tables_data, dry_run, table_sequence, skip_status_check=False)

def process_etl_pipeline(tables_data: Dict[str, Union[pd.DataFrame, List[Dict[str, Any]]]],
                        dry_run: bool = False,
                        table_sequence: List[str] = None,
                        skip_status_check: bool = None) -> Dict[str, Any]:
    """
    Função de conveniência para processar pipeline ETL completo

    Args:
        tables_data: Dicionário com dados das tabelas {table_name: [data]}
        dry_run: Se True, apenas simula o processamento
        table_sequence: Sequência customizada de tabelas (opcional)
        skip_status_check: Se None, usa configuração automática do OPERATION_MODE
                          Se True, força modo rápido (sem verificação)
                          Se False, força modo seguro (com verificação)

    Returns:
        Dict com resultados do processamento
    """
    # Usa configuração automática se não especificado
    if skip_status_check is None:
        skip_status_check = not OPERATION_MODE['verify_status']

    logger.info(f"🔧 Modo de operação: {OPERATION_MODE['description']}")

    client = create_salesforce_client()

    try:
        result = client.process_multiple_tables(tables_data, dry_run, table_sequence, skip_status_check)
        return result
    finally:
        client.close()

def validate_salesforce_config() -> tuple:
    """Valida configuração do Salesforce"""
    required_fields = ['client_id', 'client_secret', 'auth_uri', 'rest_uri']
    missing_fields = []
    
    for field in required_fields:
        if not SALESFORCE_CONFIG.get(field):
            missing_fields.append(field)
    
    is_valid = len(missing_fields) == 0
    return is_valid, missing_fields

def test_salesforce_connection() -> bool:
    """Testa conexão com Salesforce"""
    # TODO: AGENTE_4 implementar teste completo de conexão
    client = create_salesforce_client()
    
    try:
        success = client.authenticate()
        if success:
            logger.info("✅ Conexão com Salesforce OK")
        else:
            logger.error("❌ Falha na conexão com Salesforce")
        return success
    finally:
        client.close()

# =============================================================================
# MAIN PARA TESTES
# =============================================================================

if __name__ == "__main__":
    # Testa cliente Salesforce
    import sys
    
    logging.basicConfig(level=logging.INFO)
    
    print("=== TESTE SALESFORCE CLIENT ===")
    
    # Valida configuração
    is_valid, missing = validate_salesforce_config()
    print(f"Configuração válida: {is_valid}")
    if not is_valid:
        print(f"Campos ausentes: {missing}")
        sys.exit(1)
    
    # Testa conexão
    if test_salesforce_connection():
        print("✅ Teste de conexão passou")
    else:
        print("❌ Teste de conexão falhou")
        sys.exit(1)
    
    # Testa dados de exemplo (dry run)
    test_data = [
        {'id': 1, 'nome': 'Teste 1', 'email': '<EMAIL>'},
        {'id': 2, 'nome': 'Teste 2', 'email': '<EMAIL>'}
    ]
    
    result = send_data_to_salesforce('tb_clientes', test_data, dry_run=True)
    print(f"Resultado do dry run: {result}")
    
    print("✅ Todos os testes passaram!")