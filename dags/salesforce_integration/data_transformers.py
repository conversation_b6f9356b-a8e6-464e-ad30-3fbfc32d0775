"""
ETL Consolidado - Transformações de Dados
Consolida todas as transformações de dados do pipeline ETL para Salesforce Marketing Cloud.

RESPONSABILIDADES:
- Transformar dados de clientes (NewCon + Orbbits)
- Transformar dados de leads (NewCon + RD Station + Orbbits)  
- Transformar dados de produtos (NewCon + Orbbits preços)
- Transformar dados de propostas (NewCon + Orbbits pagamentos/vendas)
- Validar qualidade de dados
- Aplicar regras de negócio
"""

import pandas as pd
import numpy as np
import logging
from datetime import datetime
from typing import Dict, List, Any, Optional, Tuple

from salesforce_integration.utils import *
from salesforce_integration.config import DATA_EXTENSIONS
try:
    from salesforce_integration.test_data_manager import mark_as_test_data, validate_test_safety
except ImportError:
    # Fallback functions se o módulo de testes não estiver disponível
    def mark_as_test_data(data, table_name):
        """Fallback function para marcar dados de teste"""
        return data

    def validate_test_safety(data, table_name):
        """Fallback function para validar segurança de teste"""
        return True

# =============================================================================
# CONFIGURAÇÃO DE LOGGING
# =============================================================================

logger = setup_logging()

# =============================================================================
# CAMPOS OBRIGATÓRIOS POR TABELA (baseado no planning.md)
# =============================================================================

REQUIRED_FIELDS = {
    'tb_clientes': ['cnpjcpf', 'email'],
    'tb_leads': ['cnpjcpf', 'dt_simulacao'], 
    'tb_produtos': ['id_produto'],
    'tb_propostas': ['idproposta', 'email']
}

# =============================================================================
# TRANSFORMAÇÃO DE CLIENTES
# =============================================================================

def transform_clients(newcon_data: pd.DataFrame, orbbits_data: pd.DataFrame) -> pd.DataFrame:
    """
    Transforma dados de clientes consolidando NewCon + Orbbits.

    REGRAS DE NEGÓCIO:
    - Join por id_documento (NewCon) = contractnumber (Orbbits)
    - Campos obrigatórios: cnpjcpf, email
    - Aplicar opt-ins baseado em LGPD
    - Formatar telefones, CPFs e datas
    - Adicionar origem do Orbbits
    - Deduplicação por CNPJCPF/email (mantém registro mais recente)

    Args:
        newcon_data: DataFrame com dados de clientes do NewCon
        orbbits_data: DataFrame com dados de origem do Orbbits

    Returns:
        DataFrame transformado para tb_clientes
    """
    logger.info("Iniciando transformação de clientes...")
    
    try:
        # 1. JOIN PRINCIPAL: NewCon + Orbbits
        df_clients = newcon_data.merge(
            orbbits_data, 
            left_on='id_documento', 
            right_on='contractnumber', 
            how='left'
        )
        
        logger.info(f"Join realizado: {len(newcon_data)} registros NewCon + {len(orbbits_data)} registros Orbbits = {len(df_clients)} registros finais")
        
        # 2. APLICAR REGRAS DE OPT-IN BASEADO EM LGPD
        optin_condicoes = [
            df_clients['lgpd'] == 1,
            df_clients['lgpd'] == 0
        ]
        optin_valores = [1, 0]
        
        # Opt-ins para consórcio
        df_clients['Optin_consorcio_email'] = np.select(optin_condicoes, optin_valores, default=None)
        df_clients['Optin_consorcio_SMS'] = np.select(optin_condicoes, optin_valores, default=None)
        df_clients['Optin_consorcio_whatsapp'] = np.select(optin_condicoes, optin_valores, default=None)
        
        # Opt-ins para digital
        df_clients['Optin_digital_email'] = np.select(optin_condicoes, optin_valores, default=None)
        df_clients['Optin_digital_SMS'] = np.select(optin_condicoes, optin_valores, default=None)
        df_clients['Optin_digital_whatsapp'] = np.select(optin_condicoes, optin_valores, default=None)
        
        # 3. ADICIONAR ORIGEM DO ORBBITS
        df_clients['Origem'] = df_clients['origin']
        
        # 4. FORMATAÇÃO DE DADOS
        # Formatar telefones
        df_clients['Celular'] = df_clients['Celular'].apply(
            lambda x: format_phone(str(x)) if pd.notna(x) else None
        )
        
        # Formatar CPF/CNPJ
        df_clients['CNPJCPF'] = df_clients['CNPJCPF'].apply(
            lambda x: format_cpf(str(x)) if pd.notna(x) and len(str(x).replace('.', '').replace('-', '').replace('/', '')) == 11 
            else format_cnpj(str(x)) if pd.notna(x) else None
        )
        
        # Formatar datas para Salesforce
        df_clients['dt_nascimento'] = df_clients['dt_nascimento'].apply(
            lambda x: format_date_salesforce(x) if pd.notna(x) else None
        )
        df_clients['dt_cadastro'] = df_clients['dt_cadastro'].apply(
            lambda x: format_date_salesforce(x) if pd.notna(x) else None
        )
        
        # 5. SELEÇÃO DE CAMPOS FINAIS (baseado na lógica original)
        df_clients_final = df_clients[[
            'end_point', 'id_documento', 'CNPJCPF', 'PrimeiroNome', 'NomeCompleto', 
            'Cidade', 'Estado', 'dt_nascimento', 'Celular', 'Locale', 'e_mail', 
            'Genero', 'PontoVenda', 'dt_cadastro', 'Renda', 'politicamente_exposto', 
            'Origem', 'Optin_consorcio_email', 'Optin_consorcio_SMS', 'Optin_consorcio_whatsapp',
            'Optin_seguros_email', 'Optin_seguros_SMS', 'Optin_seguros_wpp', 
            'Optin_digital_email', 'Optin_digital_SMS', 'Optin_digital_whatsapp',
            'Optin_capital_email', 'Optin_capital_SMS', 'Optin_capital_whatsapp',
            'Campaign', 'Source', 'Medium', 'Term', 'Content', 
            'cliente_consorcio', 'cliente_seguros', 'cliente_digital', 'cliente_capital'
        ]]
        
        # 6. RENOMEAR CAMPO EMAIL PARA PADRONIZAÇÃO
        df_clients_final = df_clients_final.rename(columns={'e_mail': 'email'})

        # 7. DEDUPLICAÇÃO POR CNPJCPF/EMAIL
        # Remove duplicatas mantendo o registro mais recente (baseado em dt_cadastro)
        df_clients_final = df_clients_final.sort_values('dt_cadastro', ascending=False)
        df_clients_final = df_clients_final.drop_duplicates(subset=['CNPJCPF', 'email'], keep='first')

        logger.info(f"Transformação de clientes concluída: {len(df_clients_final)} registros após deduplicação")
        return df_clients_final
        
    except Exception as e:
        logger.error(f"Erro na transformação de clientes: {e}")
        raise

# =============================================================================
# TRANSFORMAÇÃO DE LEADS
# =============================================================================

def _prepare_rdstation_for_leads(rdstation_data: pd.DataFrame) -> pd.DataFrame:
    """
    Prepara dados do RD Station para união com dados NewCon+Orbits.

    Mapeia campos do RD Station para o formato esperado na tabela tb_leads.

    Args:
        rdstation_data: DataFrame com dados do RD Station

    Returns:
        DataFrame formatado para união com NewCon+Orbits
    """
    logger.info("Preparando dados do RD Station para integração...")

    try:
        df_rd = rdstation_data.copy()

        # Mapeamento de campos RD Station -> tb_leads (baseado na estrutura real do _process_contact)
        df_rd_mapped = pd.DataFrame()

        # Campos obrigatórios e principais (usando os nomes corretos do RD Station)
        df_rd_mapped['end_point'] = 'RD Station'
        df_rd_mapped['CNPJCPF'] = df_rd.get('CNPJCPF', '')  # Já processado no _process_contact
        df_rd_mapped['Email'] = df_rd.get('Email', '')      # Já processado no _process_contact
        df_rd_mapped['PrimeiroNome'] = df_rd.get('PrimeiroNome', '')
        df_rd_mapped['NomeCompleto'] = df_rd.get('NomeCompleto', '')
        df_rd_mapped['Celular'] = df_rd.get('Celular', '')
        df_rd_mapped['Locale'] = df_rd.get('Locale', 'br')

        # Campos de localização
        df_rd_mapped['Cidade'] = df_rd.get('Cidade', '')
        df_rd_mapped['Estado'] = df_rd.get('Estado', '')

        # Campos de data
        df_rd_mapped['Dt_cadastro'] = df_rd.get('Dt_cadastro', '')
        df_rd_mapped['Dt_simulacao'] = df_rd.get('Dt_simulacao', '')
        df_rd_mapped['dt_nascimento'] = df_rd.get('dt_nascimento', '')

        # Campos de negócio específicos do RD Station (usando valores já definidos)
        df_rd_mapped['TipoEmpresa'] = df_rd.get('TipoEmpresa', 'Bamaq Consórcio')
        df_rd_mapped['TipoBem'] = df_rd.get('TipoBem', 'Carta de Crédito')
        df_rd_mapped['TipoSimulacao'] = df_rd.get('TipoSimulacao', 'Consórcio')
        # Aplicar conversão segura de float para valores monetários
        def safe_float_conversion_rd(value):
            """Converte valor para float de forma segura, tratando strings complexas"""
            if pd.isna(value) or value is None:
                return None
            
            # Se já é um número, retorna como float
            if isinstance(value, (int, float)):
                return float(value)
            
            # Se é string, tenta extrair apenas números e pontos/vírgulas
            if isinstance(value, str):
                # Remove espaços e converte para string
                value = str(value).strip()
                
                # Se está vazio após strip, retorna None
                if not value:
                    return None
                
                # Tenta extrair apenas a primeira parte numérica (antes de '=' ou outros caracteres)
                import re
                # Remove símbolos de moeda e espaços extras
                value_clean = re.sub(r'[R$\s]+', '', value)
                # Procura por padrão numérico brasileiro (ex: 82.940,31 ou 19096,20)
                match = re.match(r'^(\d+(?:\.\d{3})*(?:,\d+)?)', value_clean)
                if match:
                    try:
                        # Converte formato brasileiro para americano
                        numeric_value = match.group(1)
                        # Se tem ponto e vírgula, trata como milhar.decimal
                        if '.' in numeric_value and ',' in numeric_value:
                            numeric_value = numeric_value.replace('.', '').replace(',', '.')
                        # Se tem apenas vírgula, trata como decimal
                        elif ',' in numeric_value:
                            numeric_value = numeric_value.replace(',', '.')
                        return float(numeric_value)
                    except ValueError:
                        logger.warning(f"Não foi possível converter ValorSimulacao: {value}")
                        return None
                
                # Se não encontrou padrão numérico, tenta conversão direta
                try:
                    return float(value.replace(',', '.'))
                except ValueError:
                    logger.warning(f"ValorSimulacao inválido ignorado: {value}")
                    return None
            
            # Para outros tipos, tenta conversão direta
            try:
                return float(value)
            except (ValueError, TypeError):
                logger.warning(f"Tipo de ValorSimulacao não suportado: {type(value)} - {value}")
                return None
        
        df_rd_mapped['ValorSimulacao'] = df_rd.get('ValorSimulacao', '').apply(safe_float_conversion_rd) if hasattr(df_rd.get('ValorSimulacao', ''), 'apply') else safe_float_conversion_rd(df_rd.get('ValorSimulacao', ''))

        # Campos de origem/campanha
        df_rd_mapped['Campaign'] = df_rd.get('Campaign', '')
        df_rd_mapped['Source'] = df_rd.get('Source', '')
        df_rd_mapped['Medium'] = df_rd.get('Medium', '')
        df_rd_mapped['Term'] = df_rd.get('Term', '')
        df_rd_mapped['Content'] = df_rd.get('Content', '')

        # Ponto de venda e vendedor
        df_rd_mapped['PontoVenda'] = df_rd.get('PontoVenda', '')
        df_rd_mapped['PlanoVenda'] = df_rd.get('PlanoVenda', '')
        df_rd_mapped['NomeVendedor'] = df_rd.get('Nome vendedor', '')  # Nome correto do campo

        # Opt-ins - usar valores do RD Station (já processados como 'True'/'')
        df_rd_mapped['Optin_consorcio_email'] = df_rd.get('Optin_consorcio_email', 'True')
        df_rd_mapped['Optin_consorcio_SMS'] = df_rd.get('Optin_consorcio_SMS', 'True')
        df_rd_mapped['Optin_consorcio_whatsapp'] = df_rd.get('Optin_consorcio_whatsapp', 'True')
        df_rd_mapped['Optin_seguros_email'] = df_rd.get('Optin_seguros_email', '')
        df_rd_mapped['Optin_seguros_SMS'] = df_rd.get('Optin_seguros_SMS', '')
        df_rd_mapped['Optin_seguros_wpp'] = df_rd.get('Optin_seguros_wpp', '')
        df_rd_mapped['Optin_digital_email'] = df_rd.get('Optin_digital_email', '')
        df_rd_mapped['Optin_digital_SMS'] = df_rd.get('Optin_digital_SMS', '')
        df_rd_mapped['Optin_digital_whatsapp'] = df_rd.get('Optin_digital_whatsapp', '')
        df_rd_mapped['Optin_capital_email'] = df_rd.get('Optin_capital_email', '')
        df_rd_mapped['Optin_capital_SMS'] = df_rd.get('Optin_capital_SMS', '')
        df_rd_mapped['Optin_capital_whatsapp'] = df_rd.get('Optin_capital_whatsapp', '')

        # Formatação de dados (dados do RD Station já vêm formatados, apenas validação)
        # Os dados já foram processados no _process_contact, então apenas garantimos que não há valores vazios

        # Limpar campos vazios
        for col in df_rd_mapped.columns:
            df_rd_mapped[col] = df_rd_mapped[col].apply(
                lambda x: None if pd.isna(x) or str(x).strip() == '' else x
            )

        # Remove registros sem CPF ou email (campos obrigatórios)
        df_rd_mapped = df_rd_mapped[
            (df_rd_mapped['CNPJCPF'].notna()) & (df_rd_mapped['CNPJCPF'].str.strip() != '') &
            (df_rd_mapped['Email'].notna()) & (df_rd_mapped['Email'].str.strip() != '')
        ]

        logger.info(f"RD Station preparado: {len(df_rd_mapped)} registros válidos de {len(rdstation_data)} originais")
        return df_rd_mapped

    except Exception as e:
        logger.error(f"Erro ao preparar dados do RD Station: {e}")
        # Retorna DataFrame vazio em caso de erro para não quebrar o pipeline
        return pd.DataFrame()

def transform_leads(newcon_data: pd.DataFrame, rdstation_data: pd.DataFrame, orbbits_data: pd.DataFrame) -> pd.DataFrame:
    """
    Transforma dados de leads consolidando NewCon + RD Station + Orbbits.
    
    REGRAS DE NEGÓCIO:
    - Consolidar múltiplas fontes de leads
    - Deduplicação por CPF/email
    - Campos obrigatórios: cnpjcpf, dt_simulacao
    - Mapeamento de campos customizados
    - Aplicar opt-ins baseado em LGPD
    
    Args:
        newcon_data: DataFrame com leads do NewCon
        rdstation_data: DataFrame com leads do RD Station  
        orbbits_data: DataFrame com dados de origem do Orbbits
        
    Returns:
        DataFrame transformado para tb_leads
    """
    logger.info("Iniciando transformação de leads...")
    
    try:
        # 1. JOIN NEWCON + ORBBITS
        df_leads = newcon_data.merge(
            orbbits_data,
            left_on='id_documento',
            right_on='contractnumber', 
            how='left'
        )
        
        logger.info(f"Join NewCon + Orbbits: {len(newcon_data)} + {len(orbbits_data)} = {len(df_leads)} registros")
        
        # 2. CONSOLIDAR COM RD STATION (se disponível)
        if rdstation_data is not None and not rdstation_data.empty:
            logger.info(f"Consolidando {len(rdstation_data)} registros do RD Station...")

            # Preparar dados do RD Station para união
            df_rdstation = _prepare_rdstation_for_leads(rdstation_data)

            # UNION ALL: NewCon+Orbits + RD Station
            df_leads = pd.concat([df_leads, df_rdstation], ignore_index=True, sort=False)

            logger.info(f"Consolidação concluída: {len(df_leads)} registros totais após UNION ALL")
        else:
            logger.info("RD Station não disponível ou vazio - usando apenas NewCon + Orbbits")
        
        # 3. APLICAR REGRAS DE OPT-IN BASEADO EM LGPD
        optin_condicoes = [
            df_leads['lgpd'] == 1,
            df_leads['lgpd'] == 0
        ]
        optin_valores = [1, 0]
        
        # Opt-ins para consórcio
        df_leads['Optin_consorcio_email'] = np.select(optin_condicoes, optin_valores, default=None)
        df_leads['Optin_consorcio_SMS'] = np.select(optin_condicoes, optin_valores, default=None)
        df_leads['Optin_consorcio_whatsapp'] = np.select(optin_condicoes, optin_valores, default=None)
        
        # Opt-ins para digital
        df_leads['Optin_digital_email'] = np.select(optin_condicoes, optin_valores, default=None)
        df_leads['Optin_digital_SMS'] = np.select(optin_condicoes, optin_valores, default=None)
        df_leads['Optin_digital_whatsapp'] = np.select(optin_condicoes, optin_valores, default=None)
        
        # 4. FORMATAÇÃO DE DADOS
        # Formatar telefones
        df_leads['Celular'] = df_leads['Celular'].apply(
            lambda x: format_phone(str(x)) if pd.notna(x) else None
        )
        
        # Formatar CPF/CNPJ
        df_leads['CNPJCPF'] = df_leads['CNPJCPF'].apply(
            lambda x: format_cpf(str(x)) if pd.notna(x) and len(str(x).replace('.', '').replace('-', '').replace('/', '')) == 11 
            else format_cnpj(str(x)) if pd.notna(x) else None
        )
        
        # Formatar datas para Salesforce
        df_leads['dt_nascimento'] = df_leads['dt_nascimento'].apply(
            lambda x: format_date_salesforce(x) if pd.notna(x) else None
        )
        df_leads['Dt_cadastro'] = df_leads['Dt_cadastro'].apply(
            lambda x: format_date_salesforce(x) if pd.notna(x) else None
        )
        df_leads['Dt_simulacao'] = df_leads['Dt_simulacao'].apply(
            lambda x: format_date_salesforce(x) if pd.notna(x) else None
        )
        
        # 5. PREENCHIMENTO DE CAMPOS OBRIGATÓRIOS VAZIOS
        df_leads['end_point'] = df_leads['end_point'].fillna('Leads')
        df_leads['end_point'] = df_leads['end_point'].replace('', 'Leads')
        
        # 6. SELEÇÃO DE CAMPOS FINAIS (baseado na lógica original)
        df_leads_final = df_leads[[
            'end_point', 'CNPJCPF', 'Email', 'Dt_cadastro', 'Celular', 'Locale', 
            'PrimeiroNome', 'NomeCompleto', 'Cidade', 'Estado', 'TipoEmpresa', 
            'TipoBem', 'TipoSimulacao', 'ValorSimulacao', 'Campaign', 'Source', 
            'Medium', 'Term', 'Content', 'PontoVenda', 'PlanoVenda', 'dt_nascimento',
            'Optin_consorcio_email', 'Optin_consorcio_SMS', 'Optin_consorcio_whatsapp',
            'Optin_seguros_email', 'Optin_seguros_SMS', 'Optin_seguros_wpp',
            'Optin_digital_email', 'Optin_digital_SMS', 'Optin_digital_whatsapp',
            'Optin_capital_email', 'Optin_capital_SMS', 'Optin_capital_whatsapp',
            'Dt_simulacao', 'NomeVendedor'
        ]]
        
        # 6. DEDUPLICAÇÃO POR CPF/EMAIL
        # Remove duplicatas mantendo o registro mais recente
        df_leads_final = df_leads_final.sort_values('Dt_simulacao', ascending=False)
        df_leads_final = df_leads_final.drop_duplicates(subset=['CNPJCPF', 'Email'], keep='first')
        
        logger.info(f"Transformação de leads concluída: {len(df_leads_final)} registros após deduplicação")
        return df_leads_final

    except Exception as e:
        logger.error(f"Erro na transformação de leads: {e}")
        raise

# =============================================================================
# TRANSFORMAÇÃO DE PRODUTOS
# =============================================================================

def transform_products(newcon_data: pd.DataFrame, orbbits_data: pd.DataFrame) -> pd.DataFrame:
    """
    Transforma dados de produtos consolidando NewCon + Orbbits (preços).

    REGRAS DE NEGÓCIO:
    - Join por id_plano_venda + id_bem (NewCon) = id_plano_de_venda + id_bem (Orbbits)
    - Gerar id_produto consolidado: id_ponto_de_venda-id_grupo-id_plano_venda-id_bem
    - Campos obrigatórios: id_produto
    - Implementar cálculos de preços
    - Adicionar campos de metadata

    Args:
        newcon_data: DataFrame com produtos do NewCon
        orbbits_data: DataFrame com preços do Orbbits

    Returns:
        DataFrame transformado para tb_produtos
    """
    logger.info("Iniciando transformação de produtos...")

    try:
        # 1. JOIN PRINCIPAL: NewCon + Orbbits (preços)
        df_product = newcon_data.merge(
            orbbits_data,
            left_on=['id_plano_venda', 'id_bem'],
            right_on=['id_plano_de_venda', 'id_bem'],
            how='left'
        )

        logger.info(f"Join realizado: {len(newcon_data)} registros NewCon + {len(orbbits_data)} registros Orbbits = {len(df_product)} registros finais")

        # 2. GERAR ID_PRODUTO CONSOLIDADO
        # Formato: id_ponto_de_venda-id_grupo-id_plano_venda-id_bem
        df_product['idProduto'] = df_product[['id_ponto_de_venda', 'id_grupo', 'id_plano_venda', 'id_bem']].astype(str).agg('-'.join, axis=1)

        # 3. FORMATAÇÃO DE DADOS
        # Função auxiliar para conversão segura de valores monetários
        def safe_float_conversion(value):
            """Converte valor para float de forma segura, tratando strings complexas"""
            if pd.isna(value) or value is None:
                return None

            # Se já é um número, retorna como float
            if isinstance(value, (int, float)):
                return float(value)

            # Se é string, tenta extrair apenas números e pontos/vírgulas
            if isinstance(value, str):
                # Remove espaços e converte para string
                value = str(value).strip()

                # Se está vazio após strip, retorna None
                if not value:
                    return None

                # Tenta extrair apenas a primeira parte numérica (antes de '=' ou outros caracteres)
                import re
                # Procura por padrão numérico no início da string
                match = re.match(r'^(\d+(?:[.,]\d+)?)', value.replace(',', '.'))
                if match:
                    try:
                        return float(match.group(1))
                    except ValueError:
                        logger.warning(f"Não foi possível converter valor monetário: {value}")
                        return None

                # Se não encontrou padrão numérico, tenta conversão direta
                try:
                    return float(value.replace(',', '.'))
                except ValueError:
                    logger.warning(f"Valor monetário inválido ignorado: {value}")
                    return None

            # Para outros tipos, tenta conversão direta
            try:
                return float(value)
            except (ValueError, TypeError):
                logger.warning(f"Tipo de valor monetário não suportado: {type(value)} - {value}")
                return None

        # Formatar valores monetários com tratamento robusto
        df_product['valor_credito'] = df_product['valor_credito'].apply(safe_float_conversion)
        df_product['valor_primeira_parcela'] = df_product['valor_primeira_parcela'].apply(safe_float_conversion)
        df_product['valor_primeira_parcela_c_seguro'] = df_product['valor_primeira_parcela_c_seguro'].apply(safe_float_conversion)

        # Formatar datas para Salesforce
        # Verifica se a coluna existe antes de tentar acessá-la
        if 'dt_atualizacao' in df_product.columns:
            df_product['dt_atualizacao'] = df_product['dt_atualizacao'].apply(
                lambda x: format_date_salesforce(x) if pd.notna(x) else None
            )
        else:
            logger.warning("Coluna 'dt_atualizacao' não encontrada nos dados de produtos")
            df_product['dt_atualizacao'] = None

        # 4. SELEÇÃO DE CAMPOS FINAIS (baseado na lógica original)
        df_product_final = df_product[[
            'end_point', 'idProduto', 'grupo', 'descricao', 'tipo_bem', 'categoria',
            'valor_credito', 'qtd_parcelas', 'reducao_contemplacao', 'taxa_admin',
            'fundo_reserva', 'dt_atualizacao', 'valor_primeira_parcela',
            'valor_primeira_parcela_c_seguro', 'valor_parcelas', 'valor_parcelas_c_seguro'
        ]]

        # 5. RENOMEAR CAMPO PARA PADRONIZAÇÃO
        df_product_final = df_product_final.rename(columns={'idProduto': 'id_produto'})

        logger.info(f"Transformação de produtos concluída: {len(df_product_final)} registros")
        return df_product_final

    except Exception as e:
        logger.error(f"Erro na transformação de produtos: {e}")
        raise

# =============================================================================
# TRANSFORMAÇÃO DE PROPOSTAS
# =============================================================================

def transform_proposals(newcon_data: pd.DataFrame, orbbits_payments: pd.DataFrame, orbbits_sales: pd.DataFrame) -> pd.DataFrame:
    """
    Transforma dados de propostas consolidando NewCon + Orbbits (pagamentos/vendas).

    REGRAS DE NEGÓCIO:
    - Join NewCon + Orbbits pagamentos por IdProposta = newcon_proposal_contract_number
    - Join NewCon + Orbbits vendas por IdProposta = proposal_id
    - Campos obrigatórios: idproposta, email
    - Implementar lógica de status de pagamento
    - Adicionar campos calculados (links, códigos)
    - Implementar validação de consistência

    Args:
        newcon_data: DataFrame com propostas do NewCon
        orbbits_payments: DataFrame com pagamentos do Orbbits
        orbbits_sales: DataFrame com vendas do Orbbits

    Returns:
        DataFrame transformado para tb_propostas
    """
    logger.info("Iniciando transformação de propostas...")

    try:
        # 1. CONVERSÃO DE TIPOS
        # Garantir que IdProposta seja string para joins
        df_proposals = newcon_data.copy()
        df_proposals['IdProposta'] = df_proposals['IdProposta'].astype(str)

        # 2. JOIN COM PAGAMENTOS ORBBITS
        df_proposals = df_proposals.merge(
            orbbits_payments,
            left_on='IdProposta',
            right_on='newcon_proposal_contract_number',
            how='left'
        )

        logger.info(f"Join com pagamentos: {len(newcon_data)} + {len(orbbits_payments)} registros")

        # 3. JOIN COM VENDAS ORBBITS
        df_proposals = df_proposals.merge(
            orbbits_sales,
            left_on='IdProposta',
            right_on='proposal_id',
            how='left'
        )

        logger.info(f"Join com vendas: {len(df_proposals)} registros finais")

        # 4. APLICAR REGRAS DE NEGÓCIO PARA TIPO DE PAGAMENTO
        df_proposals['Cod_promocional'] = df_proposals['sale_coupon']

        tppagamento_condicoes = [
            (df_proposals['dt_pagamento'].notnull()) & (df_proposals['id_ponto_venda'] == 207),
            (df_proposals['dt_pagamento'].notnull()) & (df_proposals['newcon_proposal_contract_number'].isnull()),
            (df_proposals['dt_pagamento'].notnull()) & (df_proposals['type'] == 'bill'),
            (df_proposals['dt_pagamento'].notnull()) & (df_proposals['type'] == 'credit_card'),
            (df_proposals['dt_pagamento'].notnull()) & (df_proposals['type'] == 'pix')
        ]
        tppagamento_valores = [
            'Débito em Conta',
            'Boleto',
            'Boleto',
            'Cartão de Crédito',
            'Pix'
        ]
        df_proposals['Tipo_pagamento'] = np.select(tppagamento_condicoes, tppagamento_valores, default=None)

        # 5. GERAR LINKS E CÓDIGOS BASEADO NO TIPO DE PAGAMENTO
        df_proposals['link_boleto'] =  df_proposals['payment_link_bill']
        df_proposals['cod_pix'] = df_proposals['payment_link_pix']
        df_proposals['link_cartao'] = df_proposals['payment_link_credit_card']

        # 6. FORMATAÇÃO DE DATAS
        df_proposals['dt_proposta'] = df_proposals['dt_proposta'].apply(
            lambda x: format_date_salesforce(x) if pd.notna(x) else None
        )
        df_proposals['dt_pagamento'] = df_proposals['dt_pagamento'].apply(
            lambda x: format_date_salesforce(x) if pd.notna(x) else None
        )
        df_proposals['dt_validade'] = df_proposals['dt_validade'].apply(
            lambda x: format_date_salesforce(x) if pd.notna(x) else None
        )
        df_proposals['dt_venda'] = df_proposals['dt_venda'].apply(
            lambda x: format_date_salesforce(x) if pd.notna(x) else None
        )
        df_proposals['dt_ultimaModificacao'] = df_proposals['dt_ultimaModificacao'].apply(
            lambda x: format_date_salesforce(x) if pd.notna(x) else None
        )
        df_proposals['dt_fechamento'] = df_proposals['dt_fechamento'].apply(
            lambda x: format_date_salesforce(x) if pd.notna(x) else None
        )

        # 7. SELEÇÃO DE CAMPOS FINAIS (baseado na lógica original)
        df_proposals_final = df_proposals[[
            'end_point', 'IdProposta', 'CNPJCPF', 'Email', 'PrimeiroNome', 'NomeCompleto',
            'Celular', 'dt_proposta', 'dt_pagamento', 'dt_validade', 'Status_proposta',
            'Tipo_pagamento', 'Valor', 'Cod_promocional', 'link_boleto', 'cod_pix',
            'link_cartao', 'id_produto', 'seguro', 'status_contrato', 'dt_venda',
            'ponto_venda', 'vendedor', 'dt_ultimaModificacao', 'dt_fechamento'
        ]]

        # 8. RENOMEAR CAMPOS PARA PADRONIZAÇÃO
        df_proposals_final = df_proposals_final.rename(columns={
            'IdProposta': 'idproposta',
            'Email': 'email'
        })

        logger.info(f"Transformação de propostas concluída: {len(df_proposals_final)} registros")
        return df_proposals_final

    except Exception as e:
        logger.error(f"Erro na transformação de propostas: {e}")
        raise

# =============================================================================
# VALIDAÇÃO DE QUALIDADE DE DADOS
# =============================================================================

def validate_data_quality(data: pd.DataFrame, table_name: str) -> Dict[str, Any]:
    """
    Valida qualidade de dados para uma tabela específica.

    VALIDAÇÕES IMPLEMENTADAS:
    - Campos obrigatórios presentes e não vazios
    - Formato de email válido
    - Formato de CPF/CNPJ válido
    - Formato de telefone válido
    - Duplicatas por campos únicos
    - Consistência de dados

    Args:
        data: DataFrame com dados para validação
        table_name: Nome da tabela (tb_clientes, tb_leads, tb_produtos, tb_propostas)

    Returns:
        Dict com relatório de qualidade de dados
    """
    logger.info(f"Iniciando validação de qualidade para {table_name}...")

    validation_report = {
        'table_name': table_name,
        'total_records': len(data),
        'valid_records': 0,
        'invalid_records': 0,
        'issues': [],
        'quality_score': 0,
        'field_validations': {}
    }

    try:
        # 1. VALIDAÇÃO DE CAMPOS OBRIGATÓRIOS
        required_fields = REQUIRED_FIELDS.get(table_name, [])
        missing_required = []

        for field in required_fields:
            if field not in data.columns:
                missing_required.append(f"Campo obrigatório '{field}' não encontrado")
            else:
                null_count = data[field].isnull().sum()
                empty_count = (data[field].astype(str).str.strip() == '').sum()
                total_invalid = null_count + empty_count

                validation_report['field_validations'][field] = {
                    'null_count': int(null_count),
                    'empty_count': int(empty_count),
                    'valid_count': len(data) - total_invalid,
                    'validity_rate': ((len(data) - total_invalid) / len(data)) * 100 if len(data) > 0 else 0
                }

                if total_invalid > 0:
                    validation_report['issues'].append(
                        f"Campo obrigatório '{field}': {total_invalid} registros inválidos ({null_count} nulos, {empty_count} vazios)"
                    )

        if missing_required:
            validation_report['issues'].extend(missing_required)

        # 2. VALIDAÇÃO DE FORMATO DE EMAIL
        if 'email' in data.columns:
            invalid_emails = 0
            for idx, email in data['email'].items():
                if pd.notna(email) and not validate_email(str(email)):
                    invalid_emails += 1

            if invalid_emails > 0:
                validation_report['issues'].append(f"Email: {invalid_emails} registros com formato inválido")
                validation_report['field_validations']['email'] = {
                    'invalid_format_count': invalid_emails,
                    'validity_rate': ((len(data) - invalid_emails) / len(data)) * 100
                }

        # 3. VALIDAÇÃO DE CPF/CNPJ
        cpf_field = 'cnpjcpf' if 'cnpjcpf' in data.columns else 'CNPJCPF'
        if cpf_field in data.columns:
            invalid_cpf_cnpj = 0
            for idx, doc in data[cpf_field].items():
                if pd.notna(doc):
                    doc_clean = str(doc).replace('.', '').replace('-', '').replace('/', '')
                    if len(doc_clean) == 11 and not validate_cpf(doc_clean):
                        invalid_cpf_cnpj += 1
                    elif len(doc_clean) == 14 and not validate_cnpj(doc_clean):
                        invalid_cpf_cnpj += 1

            if invalid_cpf_cnpj > 0:
                validation_report['issues'].append(f"CPF/CNPJ: {invalid_cpf_cnpj} registros com formato inválido")
                validation_report['field_validations'][cpf_field] = {
                    'invalid_format_count': invalid_cpf_cnpj,
                    'validity_rate': ((len(data) - invalid_cpf_cnpj) / len(data)) * 100
                }

        # 4. VALIDAÇÃO DE TELEFONE
        phone_field = 'Celular' if 'Celular' in data.columns else 'celular'
        if phone_field in data.columns:
            invalid_phones = 0
            for idx, phone in data[phone_field].items():
                if pd.notna(phone) and not validate_phone(str(phone)):
                    invalid_phones += 1

            if invalid_phones > 0:
                validation_report['issues'].append(f"Telefone: {invalid_phones} registros com formato inválido")
                validation_report['field_validations'][phone_field] = {
                    'invalid_format_count': invalid_phones,
                    'validity_rate': ((len(data) - invalid_phones) / len(data)) * 100
                }

        # 5. VALIDAÇÃO DE DUPLICATAS
        duplicate_fields = {
            'tb_clientes': ['cnpjcpf', 'email'],
            'tb_leads': ['cnpjcpf', 'email'],
            'tb_produtos': ['id_produto'],
            'tb_propostas': ['idproposta']
        }

        if table_name in duplicate_fields:
            fields_to_check = [f for f in duplicate_fields[table_name] if f in data.columns]
            if fields_to_check:
                duplicates = data.duplicated(subset=fields_to_check, keep=False).sum()
                if duplicates > 0:
                    validation_report['issues'].append(f"Duplicatas: {duplicates} registros duplicados nos campos {fields_to_check}")

        # 6. CÁLCULO DE QUALIDADE GERAL
        total_issues = len(validation_report['issues'])
        validation_report['quality_score'] = max(0, 100 - (total_issues * 5))  # Cada issue reduz 5 pontos
        validation_report['valid_records'] = len(data) - sum([
            validation_report['field_validations'].get(field, {}).get('invalid_format_count', 0)
            for field in validation_report['field_validations']
        ])
        validation_report['invalid_records'] = len(data) - validation_report['valid_records']

        logger.info(f"Validação concluída para {table_name}: {validation_report['quality_score']}% de qualidade")
        return validation_report

    except Exception as e:
        logger.error(f"Erro na validação de qualidade para {table_name}: {e}")
        validation_report['issues'].append(f"Erro na validação: {str(e)}")
        return validation_report

# =============================================================================
# FORMATAÇÃO PARA SALESFORCE
# =============================================================================

def format_data_for_salesforce(data: pd.DataFrame, table_name: str) -> pd.DataFrame:
    """
    Aplica formatações finais para compatibilidade com Salesforce Marketing Cloud.

    FORMATAÇÕES APLICADAS:
    - Conversão de NaN para None/null
    - Limitação de tamanho de campos texto
    - Formatação de números decimais
    - Sanitização de caracteres especiais

    Args:
        data: DataFrame com dados transformados
        table_name: Nome da tabela de destino

    Returns:
        DataFrame formatado para Salesforce
    """
    logger.info(f"Aplicando formatações finais para Salesforce: {table_name}")

    try:
        df_formatted = data.copy()

        # 1. CONVERSÃO DE NaN PARA None
        df_formatted = df_formatted.where(pd.notnull(df_formatted), None)

        # 2. SANITIZAÇÃO DE CAMPOS TEXTO
        text_fields = df_formatted.select_dtypes(include=['object']).columns
        for field in text_fields:
            df_formatted[field] = df_formatted[field].apply(
                lambda x: sanitize_string(str(x), max_length=255) if x is not None else None
            )

        # 3. FORMATAÇÃO DE NÚMEROS DECIMAIS
        numeric_fields = df_formatted.select_dtypes(include=['float64', 'int64']).columns
        for field in numeric_fields:
            df_formatted[field] = df_formatted[field].apply(
                lambda x: round(float(x), 2) if pd.notna(x) and not (np.isinf(x) if isinstance(x, (int, float)) else False) else None
            )

        # 4. VALIDAÇÃO FINAL DE CAMPOS OBRIGATÓRIOS
        required_fields = REQUIRED_FIELDS.get(table_name, [])
        for field in required_fields:
            if field in df_formatted.columns:
                # Remove registros com campos obrigatórios vazios
                df_formatted = df_formatted[
                    (df_formatted[field].notna()) &
                    (df_formatted[field].astype(str).str.strip() != '')
                ]

        logger.info(f"Formatação concluída: {len(df_formatted)} registros válidos para {table_name}")
        return df_formatted

    except Exception as e:
        logger.error(f"Erro na formatação para Salesforce: {e}")
        raise

# =============================================================================
# FUNÇÕES DE TRANSFORMAÇÃO ESPECÍFICAS POR TABELA
# =============================================================================

def transform_produtos_only(extracted_data: Dict[str, pd.DataFrame]) -> Dict[str, pd.DataFrame]:
    """
    Executa apenas a transformação de produtos.

    Args:
        extracted_data: Dict com DataFrames extraídos de todas as fontes

    Returns:
        Dict com DataFrame transformado de produtos
    """
    logger.info("Iniciando transformação específica de produtos...")

    transformed_data = {}

    try:
        # TRANSFORMAR APENAS PRODUTOS
        if 'newcon_products' in extracted_data and 'orbbits_prices' in extracted_data:
            transformed_data['tb_produtos'] = transform_products(
                extracted_data['newcon_products'],
                extracted_data['orbbits_prices']
            )

        logger.info(f"Transformação específica de produtos concluída: {len(transformed_data)} tabela processada")
        return transformed_data

    except Exception as e:
        logger.error(f"Erro na transformação específica de produtos: {e}")
        raise

def transform_clientes_only(extracted_data: Dict[str, pd.DataFrame]) -> Dict[str, pd.DataFrame]:
    """
    Executa apenas a transformação de clientes.

    Args:
        extracted_data: Dict com DataFrames extraídos de todas as fontes

    Returns:
        Dict com DataFrame transformado de clientes
    """
    logger.info("Iniciando transformação específica de clientes...")

    transformed_data = {}

    try:
        # TRANSFORMAR APENAS CLIENTES
        if 'newcon_clients' in extracted_data and 'orbbits_origin' in extracted_data:
            transformed_data['tb_clientes'] = transform_clients(
                extracted_data['newcon_clients'],
                extracted_data['orbbits_origin']
            )

        logger.info(f"Transformação específica de clientes concluída: {len(transformed_data)} tabela processada")
        return transformed_data

    except Exception as e:
        logger.error(f"Erro na transformação específica de clientes: {e}")
        raise

def transform_leads_only(extracted_data: Dict[str, pd.DataFrame]) -> Dict[str, pd.DataFrame]:
    """
    Executa apenas a transformação de leads.

    Args:
        extracted_data: Dict com DataFrames extraídos de todas as fontes

    Returns:
        Dict com DataFrame transformado de leads
    """
    logger.info("Iniciando transformação específica de leads...")

    transformed_data = {}

    try:
        # TRANSFORMAR APENAS LEADS
        if 'newcon_leads' in extracted_data and 'orbbits_origin' in extracted_data:
            rdstation_data = extracted_data.get('rdstation_leads', pd.DataFrame())
            transformed_data['tb_leads'] = transform_leads(
                extracted_data['newcon_leads'],
                rdstation_data,
                extracted_data['orbbits_origin']
            )

        logger.info(f"Transformação específica de leads concluída: {len(transformed_data)} tabela processada")
        return transformed_data

    except Exception as e:
        logger.error(f"Erro na transformação específica de leads: {e}")
        raise

def transform_propostas_only(extracted_data: Dict[str, pd.DataFrame]) -> Dict[str, pd.DataFrame]:
    """
    Executa apenas a transformação de propostas.

    Args:
        extracted_data: Dict com DataFrames extraídos de todas as fontes

    Returns:
        Dict com DataFrame transformado de propostas
    """
    logger.info("Iniciando transformação específica de propostas...")

    transformed_data = {}

    try:
        # TRANSFORMAR APENAS PROPOSTAS
        if ('newcon_proposals' in extracted_data and
            'orbbits_payments' in extracted_data and
            'orbbits_sales' in extracted_data):
            transformed_data['tb_propostas'] = transform_proposals(
                extracted_data['newcon_proposals'],
                extracted_data['orbbits_payments'],
                extracted_data['orbbits_sales']
            )

        logger.info(f"Transformação específica de propostas concluída: {len(transformed_data)} tabela processada")
        return transformed_data

    except Exception as e:
        logger.error(f"Erro na transformação específica de propostas: {e}")
        raise

# =============================================================================
# FUNÇÃO PRINCIPAL DE TRANSFORMAÇÃO
# =============================================================================

def transform_all_data(extracted_data: Dict[str, pd.DataFrame]) -> Dict[str, pd.DataFrame]:
    """
    Executa todas as transformações de dados do pipeline ETL.

    Args:
        extracted_data: Dict com DataFrames extraídos de todas as fontes

    Returns:
        Dict com DataFrames transformados prontos para carregamento
    """
    logger.info("Iniciando transformação completa de dados...")

    transformed_data = {}

    try:
        # 1. TRANSFORMAR PRODUTOS (primeira prioridade)
        if 'newcon_products' in extracted_data and 'orbbits_prices' in extracted_data:
            transformed_data['tb_produtos'] = transform_products(
                extracted_data['newcon_products'],
                extracted_data['orbbits_prices']
            )

        # 2. TRANSFORMAR CLIENTES
        if 'newcon_clients' in extracted_data and 'orbbits_origin' in extracted_data:
            transformed_data['tb_clientes'] = transform_clients(
                extracted_data['newcon_clients'],
                extracted_data['orbbits_origin']
            )

        # 3. TRANSFORMAR LEADS
        if 'newcon_leads' in extracted_data and 'orbbits_origin' in extracted_data:
            rdstation_data = extracted_data.get('rdstation_leads', pd.DataFrame())
            transformed_data['tb_leads'] = transform_leads(
                extracted_data['newcon_leads'],
                rdstation_data,
                extracted_data['orbbits_origin']
            )

        # 4. TRANSFORMAR PROPOSTAS
        if ('newcon_proposals' in extracted_data and
            'orbbits_payments' in extracted_data and
            'orbbits_sales' in extracted_data):
            transformed_data['tb_propostas'] = transform_proposals(
                extracted_data['newcon_proposals'],
                extracted_data['orbbits_payments'],
                extracted_data['orbbits_sales']
            )

        logger.info(f"Transformação completa concluída: {len(transformed_data)} tabelas processadas")
        return transformed_data

    except Exception as e:
        logger.error(f"Erro na transformação completa: {e}")
        raise

# =============================================================================
# UTILITÁRIOS DE MONITORAMENTO
# =============================================================================

def generate_transformation_report(transformed_data: Dict[str, pd.DataFrame]) -> Dict[str, Any]:
    """
    Gera relatório detalhado das transformações realizadas.

    Args:
        transformed_data: Dict com DataFrames transformados

    Returns:
        Dict com relatório de transformação
    """
    report = {
        'timestamp': datetime.now().isoformat(),
        'tables_processed': len(transformed_data),
        'total_records': sum(len(df) for df in transformed_data.values()),
        'table_details': {},
        'quality_summary': {}
    }

    for table_name, df in transformed_data.items():
        # Detalhes da tabela
        report['table_details'][table_name] = {
            'record_count': len(df),
            'column_count': len(df.columns),
            'columns': list(df.columns),
            'memory_usage_mb': df.memory_usage(deep=True).sum() / 1024 / 1024
        }

        # Validação de qualidade
        quality_report = validate_data_quality(df, table_name)
        report['quality_summary'][table_name] = {
            'quality_score': quality_report['quality_score'],
            'valid_records': quality_report['valid_records'],
            'invalid_records': quality_report['invalid_records'],
            'issues_count': len(quality_report['issues'])
        }

    return report

# =============================================================================
# MAIN PARA TESTES
# =============================================================================

if __name__ == "__main__":
    """
    Testes básicos das funções de transformação.
    Execute este arquivo diretamente para validar as implementações.
    """
    print("=== TESTE DE TRANSFORMAÇÕES DE DADOS ===")

    # Dados de teste para clientes - EXPLICITAMENTE MARCADOS COMO TESTE
    test_clients_newcon = mark_as_test_data(
        pd.DataFrame({
            'end_point': ['Clientes'] * 3,
            'id_documento': ['TEST_DOC_001', 'TEST_DOC_002', 'TEST_DOC_003'],
            'CNPJCPF': ['00000000000', '11111111111', '22222222222'],  # CPFs claramente de teste
            'PrimeiroNome': ['TEST_USER_001', 'TEST_USER_002', 'TEST_USER_003'],
            'NomeCompleto': ['TEST USER 001 EXEMPLO', 'TEST USER 002 EXEMPLO', 'TEST USER 003 EXEMPLO'],
            'Cidade': ['CIDADE_TESTE', 'CIDADE_TESTE', 'CIDADE_TESTE'],
            'Estado': ['TS', 'TS', 'TS'],  # Estado fictício para teste
            'dt_nascimento': ['1990-01-15', '1985-05-20', '1992-12-10'],
            'Celular': ['11999999999', '21888888888', '31777777777'],  # Telefones de teste
            'Locale': ['br', 'br', 'br'],
            'e_mail': ['<EMAIL>', '<EMAIL>', '<EMAIL>'],
        'Genero': ['M', 'F', 'M'],
        'PontoVenda': ['Loja A', 'Loja B', 'Loja C'],
        'dt_cadastro': ['2023-01-01', '2023-02-01', '2023-03-01'],
        'Renda': [5000.0, 7000.0, 6000.0],
        'politicamente_exposto': [0, 0, 0],
        'Optin_seguros_email': [None, None, None],
        'Optin_seguros_SMS': [None, None, None],
        'Optin_seguros_wpp': [None, None, None],
        'Optin_capital_email': [None, None, None],
        'Optin_capital_SMS': [None, None, None],
        'Optin_capital_whatsapp': [None, None, None],
        'Campaign': [None, None, None],
        'Source': [None, None, None],
        'Medium': [None, None, None],
        'Term': [None, None, None],
        'Content': [None, None, None],
            'cliente_consorcio': [1, 1, 1],
            'cliente_seguros': [None, None, None],
            'cliente_digital': [0, 0, 1],
            'cliente_capital': [None, None, None]
        }),
        description="Dados de clientes NewCon para testes de transformação",
        source_module="data_transformers"
    )

    test_orbbits_origin = mark_as_test_data(
        pd.DataFrame({
            'contractnumber': ['TEST_DOC_001', 'TEST_DOC_002', 'TEST_DOC_003'],
            'origin': ['TEST_GOOGLE', 'TEST_FACEBOOK', 'TEST_DIRECT'],
            'lgpd': [1, 1, 0]
        }),
        description="Dados de origem Orbbits para testes de transformação",
        source_module="data_transformers"
    )

    print("\n1. Testando transformação de clientes...")
    try:
        result_clients = transform_clients(test_clients_newcon, test_orbbits_origin)
        print(f"✅ Clientes transformados: {len(result_clients)} registros")
        print(f"   Colunas: {list(result_clients.columns)}")

        # Teste de validação
        validation = validate_data_quality(result_clients, 'tb_clientes')
        print(f"   Qualidade: {validation['quality_score']}% ({validation['valid_records']}/{validation['total_records']} válidos)")

    except Exception as e:
        print(f"❌ Erro no teste de clientes: {e}")

    # Dados de teste para produtos - EXPLICITAMENTE MARCADOS COMO TESTE
    test_products_newcon = mark_as_test_data(
        pd.DataFrame({
            'end_point': ['Produtos'] * 2,
            'id_grupo': [1, 2],
            'id_plano_venda': [100, 200],
            'id_taxa_plano': [1, 2],
            'id_produto': [1001, 2002],
            'id_bem': [500, 600],
            'id_ponto_de_venda': [10, 20],
            'grupo': ['GRUPO_TESTE_A', 'GRUPO_TESTE_B'],
            'descricao': ['PRODUTO TESTE A', 'PRODUTO TESTE B'],
            'tipo_bem': ['AUTOMOVEL', 'AUTOMOVEL'],
            'categoria': ['CONSORCIO', 'CONSORCIO'],
            'valor_credito': [50000.0, 75000.0],
            'qtd_parcelas': [60, 80],
            'reducao_contemplacao': [10.0, 15.0],
            'taxa_admin': [15.0, 18.0],
            'fundo_reserva': [2.0, 2.5],
            'dt_atualizacao': ['2025-01-01', '2025-01-02']
        }),
        description="Dados de produtos NewCon para testes de transformação",
        source_module="data_transformers"
    )

    test_orbbits_prices = mark_as_test_data(
        pd.DataFrame({
            'id_plano_de_venda': [100, 200],
            'id_bem': [500, 600],
            'valor_primeira_parcela': [1000.0, 1500.0],
            'valor_primeira_parcela_c_seguro': [1100.0, 1650.0],
            'valor_parcelas': ['2-60 = 800', '2-80 = 900'],
            'valor_parcelas_c_seguro': ['2-60 = 880', '2-80 = 990']
        }),
        description="Dados de preços Orbbits para testes de transformação",
        source_module="data_transformers"
    )

    print("\n2. Testando transformação de produtos...")
    try:
        result_products = transform_products(test_products_newcon, test_orbbits_prices)
        print(f"✅ Produtos transformados: {len(result_products)} registros")
        print(f"   ID Produtos gerados: {list(result_products['id_produto'])}")

        # Teste de validação
        validation = validate_data_quality(result_products, 'tb_produtos')
        print(f"   Qualidade: {validation['quality_score']}% ({validation['valid_records']}/{validation['total_records']} válidos)")

    except Exception as e:
        print(f"❌ Erro no teste de produtos: {e}")

    print("\n3. Testando validação de qualidade...")
    try:
        # Dados com problemas intencionais - EXPLICITAMENTE MARCADOS COMO TESTE
        test_invalid_data = mark_as_test_data(
            pd.DataFrame({
                'cnpjcpf': ['00000000000', 'INVALID_TEST_CPF', ''],  # CPF de teste, inválido, vazio
                'email': ['<EMAIL>', '<EMAIL>', None],  # Email válido de teste, inválido de teste, nulo
                'id_produto': ['TEST_PROD_001', 'TEST_PROD_002', 'TEST_PROD_001']  # Duplicata intencional
            }),
            description="Dados inválidos para teste de validação de qualidade",
            source_module="data_transformers"
        )

        validation = validate_data_quality(test_invalid_data, 'tb_clientes')
        print(f"✅ Validação executada: {validation['quality_score']}% de qualidade")
        print(f"   Issues encontradas: {len(validation['issues'])}")
        for issue in validation['issues']:
            print(f"   - {issue}")

    except Exception as e:
        print(f"❌ Erro no teste de validação: {e}")

    print("\n✅ Todos os testes de transformação concluídos!")

    # Validação de segurança dos dados de teste
    print("\n🔒 VALIDAÇÃO DE SEGURANÇA DOS DADOS DE TESTE:")
    test_datasets = [
        ("Clientes NewCon", test_clients_newcon),
        ("Origem Orbbits", test_orbbits_origin),
        ("Produtos NewCon", test_products_newcon),
        ("Preços Orbbits", test_orbbits_prices),
        ("Dados Inválidos", test_invalid_data)
    ]

    all_safe = True
    for name, dataset in test_datasets:
        safety_report = validate_test_safety(dataset)
        status = "✅ SEGURO" if safety_report['is_safe'] else "⚠️  ATENÇÃO"
        print(f"  {status} {name}")

        if not safety_report['is_safe']:
            all_safe = False
            for issue in safety_report['issues']:
                print(f"    - {issue}")

    print(f"\n� Status Geral de Segurança: {'✅ TODOS OS DADOS SEGUROS' if all_safe else '⚠️  REVISAR DADOS'}")

    print("\n�📋 RESUMO DA IMPLEMENTAÇÃO:")
    print("✅ transform_clients() - Consolidação NewCon + Orbbits com opt-ins LGPD e deduplicação")
    print("✅ transform_leads() - Consolidação NewCon + RD Station + Orbbits com deduplicação")
    print("✅ transform_products() - Consolidação NewCon + Orbbits preços com ID gerado")
    print("✅ transform_proposals() - Consolidação NewCon + Orbbits pagamentos/vendas")
    print("✅ validate_data_quality() - Validação completa de campos obrigatórios e formatos")
    print("✅ format_data_for_salesforce() - Formatação final para compatibilidade SF")
    print("✅ transform_all_data() - Orquestração completa de todas as transformações")
    print("✅ test_data_manager - Sistema de segurança para dados de teste implementado")
    print("✅ _prepare_rdstation_for_leads() - Integração RD Station com UNION ALL")
    print("\n🎯 FASE 3 - TRANSFORMAÇÃO DE DADOS: IMPLEMENTAÇÃO COMPLETA COM SEGURANÇA!")
    print("🆕 NOVA FUNCIONALIDADE: Integração RD Station com UNION ALL implementada!")
